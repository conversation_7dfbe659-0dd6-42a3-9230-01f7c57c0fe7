{% extends "base.html" %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Paiement de la vente #{{ sale.reference }}</h5>
                </div>
                <div class="card-body">
                    <!-- Résumé de la vente -->
                    <div class="mb-4">
                        <h6 class="fw-bold">Résumé de la vente</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <tbody>
                                    <tr>
                                        <th style="width: 150px">Total HT:</th>
                                        <td>{{ "%.2f"|format(sale.total_ht) }} €</td>
                                    </tr>
                                    <tr>
                                        <th>TVA ({{ "%.1f"|format(sale.tax_rate) }}%):</th>
                                        <td>{{ "%.2f"|format(sale.tax_amount) }} €</td>
                                    </tr>
                                    {% if sale.discount_amount > 0 %}
                                    <tr>
                                        <th>Réduction:</th>
                                        <td>-{{ "%.2f"|format(sale.discount_amount) }} €</td>
                                    </tr>
                                    {% endif %}
                                    <tr class="table-primary">
                                        <th>Total TTC:</th>
                                        <td class="fw-bold">{{ "%.2f"|format(sale.total_ttc) }} €</td>
                                    </tr>
                                    {% if sale.total_paid > 0 %}
                                    <tr>
                                        <th>Déjà payé:</th>
                                        <td>{{ "%.2f"|format(sale.total_paid) }} €</td>
                                    </tr>
                                    <tr class="table-success">
                                        <th>Reste à payer:</th>
                                        <td class="fw-bold">{{ "%.2f"|format(sale.total_ttc - sale.total_paid) }} €</td>
                                    </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Formulaire de paiement -->
                    <form method="POST" class="needs-validation" novalidate>
                        {{ form.csrf_token }}

                        <!-- Méthodes de paiement avec boutons -->
                        <div class="mb-4">
                            <label class="form-label fw-bold">Choisissez la méthode de paiement</label>
                            <div class="row g-2">
                                <div class="col-6 col-md-3">
                                    <button type="button" class="btn btn-outline-success w-100 payment-method-btn" data-method="CASH">
                                        <i class="fas fa-money-bill-wave d-block mb-1"></i>
                                        <small>Espèces</small>
                                    </button>
                                </div>
                                <div class="col-6 col-md-3">
                                    <button type="button" class="btn btn-outline-primary w-100 payment-method-btn" data-method="CARD">
                                        <i class="fas fa-credit-card d-block mb-1"></i>
                                        <small>Carte bancaire</small>
                                    </button>
                                </div>
                                <div class="col-6 col-md-3">
                                    <button type="button" class="btn btn-outline-info w-100 payment-method-btn" data-method="CHECK">
                                        <i class="fas fa-money-check d-block mb-1"></i>
                                        <small>Chèque</small>
                                    </button>
                                </div>
                                <div class="col-6 col-md-3">
                                    <button type="button" class="btn btn-outline-warning w-100 payment-method-btn" data-method="TRANSFER">
                                        <i class="fas fa-exchange-alt d-block mb-1"></i>
                                        <small>Virement</small>
                                    </button>
                                </div>
                            </div>
                            <!-- Champ caché pour stocker la méthode sélectionnée -->
                            <input type="hidden" name="method" id="selected-method" required>
                            <div class="invalid-feedback">
                                Veuillez sélectionner une méthode de paiement.
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.amount.label(class="form-label") }}
                            <div class="input-group">
                                {{ form.amount(class="form-control", value=sale.remaining_amount if sale.remaining_amount > 0 else sale.total) }}
                                <span class="input-group-text">€</span>
                            </div>
                        </div>

                        <div class="mb-3" id="reference-field" style="display: none;">
                            {{ form.reference.label(class="form-label") }}
                            {{ form.reference(class="form-control") }}
                            <div class="form-text">Numéro de transaction, chèque, etc.</div>
                        </div>

                        <div class="d-grid gap-2">
                            {{ form.submit(class="btn btn-primary btn-lg") }}
                            <a href="{{ url_for('pos.index') }}" class="btn btn-outline-secondary">Annuler</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethodBtns = document.querySelectorAll('.payment-method-btn');
    const selectedMethodInput = document.getElementById('selected-method');
    const referenceField = document.getElementById('reference-field');
    const form = document.querySelector('form');

    // Gestion des boutons de méthodes de paiement
    paymentMethodBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Retirer la sélection de tous les boutons
            paymentMethodBtns.forEach(b => {
                b.classList.remove('btn-success', 'btn-primary', 'btn-info', 'btn-warning');
                b.classList.add('btn-outline-success', 'btn-outline-primary', 'btn-outline-info', 'btn-outline-warning');
            });

            // Marquer le bouton sélectionné
            const method = this.dataset.method;
            this.classList.remove('btn-outline-success', 'btn-outline-primary', 'btn-outline-info', 'btn-outline-warning');

            // Appliquer la couleur appropriée selon la méthode
            if (method === 'CASH') {
                this.classList.add('btn-success');
            } else if (method === 'CARD') {
                this.classList.add('btn-primary');
            } else if (method === 'CHECK') {
                this.classList.add('btn-info');
            } else if (method === 'TRANSFER') {
                this.classList.add('btn-warning');
            }

            // Stocker la méthode sélectionnée
            selectedMethodInput.value = method;

            // Afficher/masquer le champ référence
            toggleReferenceField(method);

            // Retirer les messages d'erreur de validation
            selectedMethodInput.classList.remove('is-invalid');
        });
    });

    // Afficher/masquer le champ référence selon le mode de paiement
    function toggleReferenceField(method) {
        if (method === 'CARD' || method === 'CHECK') {
            referenceField.style.display = 'block';
        } else {
            referenceField.style.display = 'none';
        }
    }

    // Validation du formulaire
    form.addEventListener('submit', function(event) {
        let isValid = true;

        // Vérifier qu'une méthode de paiement est sélectionnée
        if (!selectedMethodInput.value) {
            selectedMethodInput.classList.add('is-invalid');
            isValid = false;
        }

        // Validation HTML5 standard
        if (!form.checkValidity()) {
            isValid = false;
        }

        if (!isValid) {
            event.preventDefault();
            event.stopPropagation();
        }

        form.classList.add('was-validated');
    });

    // Sélectionner automatiquement "Espèces" par défaut
    const cashBtn = document.querySelector('[data-method="CASH"]');
    if (cashBtn) {
        cashBtn.click();
    }
});
</script>

<style>
.payment-method-btn {
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: all 0.2s ease;
}

.payment-method-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.payment-method-btn i {
    font-size: 1.5rem;
}

.payment-method-btn.btn-success,
.payment-method-btn.btn-primary,
.payment-method-btn.btn-info,
.payment-method-btn.btn-warning {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}
</style>
{% endblock %}