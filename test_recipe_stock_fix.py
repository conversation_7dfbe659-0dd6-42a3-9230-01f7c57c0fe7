#!/usr/bin/env python3
"""
Script de test pour vérifier que la correction du stock des produits avec recette fonctionne
"""

import sys
import os

# Ajouter le répertoire racine au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.modules.inventory.models_product import Product
from app.modules.inventory.models_ingredient import Ingredient
from app.modules.inventory.models_recipe import Recipe, RecipeItem
from app.modules.auth.models import User

def test_recipe_product_stock():
    """Test de la vérification du stock pour les produits avec recette"""
    
    app = create_app()
    
    with app.app_context():
        print("=== Test de vérification du stock pour produits avec recette ===\n")
        
        # Rechercher un produit avec recette
        product_with_recipe = Product.query.filter_by(has_recipe=True).first()
        
        if not product_with_recipe:
            print("❌ Aucun produit avec recette trouvé.")
            return
        
        print(f"Produit testé: {product_with_recipe.name}")
        print(f"Has recipe: {product_with_recipe.has_recipe}")
        print(f"Stock direct du produit: {product_with_recipe.stock_quantity}")
        
        if product_with_recipe.recipe:
            print(f"\nRecette trouvée avec {len(product_with_recipe.recipe.items)} ingrédients:")
            
            # Afficher les ingrédients et leurs stocks
            for recipe_item in product_with_recipe.recipe.items:
                ingredient = recipe_item.ingredient
                print(f"  - {ingredient.name}: {recipe_item.quantity} {ingredient.unit} (stock: {ingredient.stock_quantity})")
            
            # Test de la méthode get_available_quantity
            available_qty = product_with_recipe.get_available_quantity()
            print(f"\n✅ Quantité disponible calculée via get_available_quantity(): {available_qty}")
            
            # Test de la méthode get_possible_quantity de la recette
            recipe_qty = product_with_recipe.recipe.get_possible_quantity()
            print(f"✅ Quantité possible via recipe.get_possible_quantity(): {recipe_qty}")
            
            # Vérifier que les deux méthodes donnent le même résultat
            if available_qty == recipe_qty:
                print("✅ Les deux méthodes de calcul sont cohérentes")
            else:
                print("❌ Incohérence entre les méthodes de calcul")
            
            # Test de vérification du stock (comme dans process_cart)
            test_quantity = 1
            print(f"\n=== Test de vérification pour {test_quantity} unité ===")
            
            if available_qty >= test_quantity:
                print(f"✅ Stock suffisant: {available_qty} >= {test_quantity}")
            else:
                print(f"❌ Stock insuffisant: {available_qty} < {test_quantity}")
            
            # Test avec une quantité plus importante
            test_quantity = 5
            print(f"\n=== Test de vérification pour {test_quantity} unités ===")
            
            if available_qty >= test_quantity:
                print(f"✅ Stock suffisant: {available_qty} >= {test_quantity}")
            else:
                print(f"❌ Stock insuffisant: {available_qty} < {test_quantity}")
                
        else:
            print("❌ Aucune recette associée au produit")
        
        # Test avec un produit simple (sans recette)
        print(f"\n{'='*60}")
        print("=== Test avec un produit simple (sans recette) ===\n")
        
        product_simple = Product.query.filter_by(has_recipe=False).first()
        
        if product_simple:
            print(f"Produit testé: {product_simple.name}")
            print(f"Has recipe: {product_simple.has_recipe}")
            print(f"Stock direct: {product_simple.stock_quantity}")
            
            available_qty_simple = product_simple.get_available_quantity()
            print(f"Quantité disponible: {available_qty_simple}")
            
            if available_qty_simple == product_simple.stock_quantity:
                print("✅ Pour les produits simples, get_available_quantity() retourne le stock direct")
            else:
                print("❌ Problème avec get_available_quantity() pour les produits simples")
        else:
            print("❌ Aucun produit simple trouvé")

if __name__ == "__main__":
    test_recipe_product_stock()
