#!/usr/bin/env python3
"""
Script de debug pour tester la méthode update_stock des produits avec recette
"""

# Simulation simple pour tester la logique
class MockIngredient:
    def __init__(self, name, stock_quantity):
        self.name = name
        self.stock_quantity = stock_quantity
    
    def update_stock(self, quantity, operation='subtract', reason=None, reference=None, notes=None):
        if operation == 'subtract':
            if self.stock_quantity >= quantity:
                self.stock_quantity -= quantity
                return True
            else:
                return False
        return True

class MockRecipeItem:
    def __init__(self, ingredient, quantity):
        self.ingredient = ingredient
        self.quantity = quantity

class MockRecipe:
    def __init__(self, items):
        self.items = items
    
    def get_possible_quantity(self):
        if not self.items:
            return 0
        
        possible_quantities = []
        for item in self.items:
            if item.quantity <= 0:
                return 0
            possible = item.ingredient.stock_quantity / item.quantity
            possible_quantities.append(int(possible))
        
        return min(possible_quantities) if possible_quantities else 0

class MockProduct:
    def __init__(self, name, has_recipe=False, recipe=None, stock_quantity=0):
        self.name = name
        self.has_recipe = has_recipe
        self.recipe = recipe
        self.stock_quantity = stock_quantity
    
    def get_available_quantity(self):
        """Retourne la quantité disponible (stock direct ou calculé via recette)"""
        direct_stock = self.stock_quantity or 0
        
        if not self.has_recipe:
            return direct_stock
        
        if self.recipe:
            recipe_stock = self.recipe.get_possible_quantity()
            return recipe_stock
        
        return direct_stock
    
    def update_stock_recipe(self, quantity, operation='subtract'):
        """Version simplifiée de update_stock pour les produits avec recette"""
        if self.has_recipe and self.recipe:
            if operation in ['subtract', 'remove']:
                # Vérifier d'abord si on a assez d'ingrédients
                for recipe_item in self.recipe.items:
                    ingredient = recipe_item.ingredient
                    ingredient_qty_needed = recipe_item.quantity * quantity
                    if ingredient.stock_quantity < ingredient_qty_needed:
                        print(f"❌ Pas assez de {ingredient.name}: besoin {ingredient_qty_needed}, disponible {ingredient.stock_quantity}")
                        return False  # Pas assez d'ingrédients
                
                # Déduire les ingrédients
                for recipe_item in self.recipe.items:
                    ingredient = recipe_item.ingredient
                    ingredient_qty_to_deduct = recipe_item.quantity * quantity
                    success = ingredient.update_stock(ingredient_qty_to_deduct, operation='subtract')
                    if not success:
                        print(f"❌ Échec de la déduction pour {ingredient.name}")
                        return False
                    print(f"✅ Déduit {ingredient_qty_to_deduct} de {ingredient.name}, nouveau stock: {ingredient.stock_quantity}")
                
                return True
            else:
                return False
        else:
            # Produit simple
            if operation == 'subtract':
                if self.stock_quantity >= quantity:
                    self.stock_quantity -= quantity
                    return True
                else:
                    return False
            return True

def test_double_burger():
    """Test avec un Double Burger simulé"""
    print("=== Test Double Burger ===\n")
    
    # Créer les ingrédients
    pain = MockIngredient("Pain", 50)
    viande = MockIngredient("Viande", 30)
    fromage = MockIngredient("Fromage", 40)
    salade = MockIngredient("Salade", 25)
    
    print("Stocks initiaux:")
    print(f"  Pain: {pain.stock_quantity}")
    print(f"  Viande: {viande.stock_quantity}")
    print(f"  Fromage: {fromage.stock_quantity}")
    print(f"  Salade: {salade.stock_quantity}")
    
    # Créer la recette (quantités par burger)
    recipe_items = [
        MockRecipeItem(pain, 2),      # 2 pains par burger
        MockRecipeItem(viande, 2),    # 2 steaks par burger
        MockRecipeItem(fromage, 2),   # 2 tranches de fromage
        MockRecipeItem(salade, 1)     # 1 portion de salade
    ]
    
    recipe = MockRecipe(recipe_items)
    
    print(f"\nRecette Double Burger:")
    for item in recipe_items:
        print(f"  {item.ingredient.name}: {item.quantity}")
    
    # Créer le produit
    double_burger = MockProduct("Double Burger", has_recipe=True, recipe=recipe)
    
    # Calculer la quantité disponible
    available = double_burger.get_available_quantity()
    print(f"\nQuantité disponible calculée: {available}")
    
    # Test de vente de 1 burger
    print(f"\n=== Test de vente de 1 Double Burger ===")
    success = double_burger.update_stock_recipe(1, 'subtract')
    
    if success:
        print("✅ Vente réussie!")
        print("\nStocks après vente:")
        print(f"  Pain: {pain.stock_quantity}")
        print(f"  Viande: {viande.stock_quantity}")
        print(f"  Fromage: {fromage.stock_quantity}")
        print(f"  Salade: {salade.stock_quantity}")
    else:
        print("❌ Vente échouée!")
    
    # Test de vente de 10 burgers (devrait échouer)
    print(f"\n=== Test de vente de 10 Double Burger ===")
    success = double_burger.update_stock_recipe(10, 'subtract')
    
    if success:
        print("✅ Vente réussie!")
    else:
        print("❌ Vente échouée (normal, pas assez d'ingrédients)!")

if __name__ == "__main__":
    test_double_burger()
